/**
 * Arabic RTL Support Service
 * Enhanced bidirectional text handling and RTL context for Arabic responses
 * Resolves LOC-001: Enhance Arabic RTL Support
 */

import { logger } from '../utils/logger';

// Arabic text direction markers
export const RTL_MARKERS = {
  RLM: '\u200F', // Right-to-Left Mark
  LRM: '\u200E', // Left-to-Right Mark
  RLE: '\u202B', // Right-to-Left Embedding
  LRE: '\u202A', // Left-to-Right Embedding
  PDF: '\u202C', // Pop Directional Formatting
  RLO: '\u202E', // Right-to-Left Override
  LRO: '\u202D', // Left-to-Right Override
} as const;

// Arabic numerals and their corresponding English numerals
export const ARABIC_NUMERALS = {
  '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
  '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
} as const;

// Common Syrian dialect variations
export const SYRIAN_DIALECTS = {
  damascus: {
    name: 'دمشقية',
    greetings: ['أهلاً وسهلاً', 'مرحبا', 'أهلين'],
    expressions: ['يعطيك العافية', 'الله يعطيك العافية', 'تسلم إيديك'],
    farewells: ['مع السلامة', 'يلا بالسلامة', 'الله معك']
  },
  aleppo: {
    name: 'حلبية',
    greetings: ['أهلاً وسهلاً', 'مرحبا', 'أهلين'],
    expressions: ['يعطيك العافية', 'الله يعطيك العافية', 'تسلم'],
    farewells: ['مع السلامة', 'بالسلامة', 'الله معك']
  },
  homs: {
    name: 'حمصية',
    greetings: ['أهلاً وسهلاً', 'مرحبا', 'أهلين'],
    expressions: ['يعطيك العافية', 'الله يعطيك العافية', 'تسلم'],
    farewells: ['مع السلامة', 'بالسلامة', 'الله معك']
  },
  latakia: {
    name: 'لاذقانية',
    greetings: ['أهلاً وسهلاً', 'مرحبا', 'أهلين'],
    expressions: ['يعطيك العافية', 'الله يعطيك العافية', 'تسلم'],
    farewells: ['مع السلامة', 'بالسلامة', 'الله معك']
  },
  general: {
    name: 'عامة',
    greetings: ['أهلاً وسهلاً', 'مرحباً', 'السلام عليكم'],
    expressions: ['يعطيك العافية', 'بارك الله فيك', 'جزاك الله خيراً'],
    farewells: ['مع السلامة', 'في أمان الله', 'الله معك']
  }
} as const;

export interface RTLTextOptions {
  forceRTL?: boolean;
  preserveNumbers?: boolean;
  useArabicNumerals?: boolean;
  dialect?: keyof typeof SYRIAN_DIALECTS;
  addDirectionalMarkers?: boolean;
}

export interface ArabicMessage {
  content: string;
  contentArabic?: string;
  direction: 'rtl' | 'ltr' | 'auto';
  dialect?: string;
  culturalContext?: {
    formality: 'formal' | 'informal' | 'professional';
    region: string;
    expressions: string[];
  };
}

class ArabicRTLService {
  /**
   * Process Arabic text with proper RTL formatting
   */
  processArabicText(text: string, options: RTLTextOptions = {}): ArabicMessage {
    try {
      const {
        forceRTL = true,
        preserveNumbers = false,
        useArabicNumerals = false,
        dialect = 'general',
        addDirectionalMarkers = true
      } = options;

      let processedText = text;

      // Add RTL markers if needed
      if (addDirectionalMarkers && this.containsArabic(text)) {
        processedText = this.addDirectionalMarkers(processedText);
      }

      // Handle numbers
      if (useArabicNumerals) {
        processedText = this.convertToArabicNumerals(processedText);
      } else if (preserveNumbers) {
        processedText = this.preserveEnglishNumbers(processedText);
      }

      // Enhance with dialect-specific expressions
      const dialectInfo = SYRIAN_DIALECTS[dialect];
      const culturalContext = {
        formality: this.detectFormality(text),
        region: dialectInfo.name,
        expressions: dialectInfo.expressions
      };

      return {
        content: processedText,
        contentArabic: processedText,
        direction: forceRTL || this.containsArabic(text) ? 'rtl' : 'ltr',
        dialect: dialectInfo.name,
        culturalContext
      };

    } catch (error) {
      logger.error('Error processing Arabic text', { error, text: text.substring(0, 100) });
      return {
        content: text,
        direction: 'auto',
      };
    }
  }

  /**
   * Create culturally appropriate Arabic response
   */
  createCulturalResponse(
    baseMessage: string,
    userRole: 'CLIENT' | 'EXPERT',
    dialect: keyof typeof SYRIAN_DIALECTS = 'general',
    context: {
      isFirstMessage?: boolean;
      stepName?: string;
      userLocation?: string;
    } = {}
  ): ArabicMessage {
    const dialectInfo = SYRIAN_DIALECTS[dialect];
    const { isFirstMessage = false, stepName, userLocation } = context;

    let enhancedMessage = baseMessage;

    // Add appropriate greeting for first message
    if (isFirstMessage) {
      const greeting = this.getRandomElement(dialectInfo.greetings);
      enhancedMessage = `${greeting}!\n\n${enhancedMessage}`;
    }

    // Add location-specific context
    if (userLocation) {
      enhancedMessage = enhancedMessage.replace(
        /سوريا/g,
        `سوريا${userLocation ? ` - ${userLocation}` : ''}`
      );
    }

    // Add encouraging expressions based on role
    if (userRole === 'EXPERT') {
      enhancedMessage += `\n\n${this.getRandomElement(dialectInfo.expressions)}! 💪`;
    } else {
      enhancedMessage += `\n\nأنا هنا لمساعدتك في كل خطوة! 😊`;
    }

    return this.processArabicText(enhancedMessage, {
      dialect,
      addDirectionalMarkers: true,
      preserveNumbers: true
    });
  }

  /**
   * Format mixed content (Arabic + English/Numbers)
   */
  formatMixedContent(content: string): string {
    // Handle URLs and preserve their LTR direction
    content = content.replace(
      /(https?:\/\/[^\s]+)/g,
      `${RTL_MARKERS.LRE}$1${RTL_MARKERS.PDF}`
    );

    // Handle email addresses
    content = content.replace(
      /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
      `${RTL_MARKERS.LRE}$1${RTL_MARKERS.PDF}`
    );

    // Handle English words in Arabic text
    content = content.replace(
      /([a-zA-Z]+)/g,
      `${RTL_MARKERS.LRM}$1${RTL_MARKERS.LRM}`
    );

    return content;
  }

  /**
   * Validate Arabic text quality
   */
  validateArabicText(text: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Check for common issues
    if (text.includes('ي') && text.includes('ى')) {
      issues.push('Mixed usage of ي and ى');
      suggestions.push('Use consistent alif maqsura (ى) or yaa (ي)');
    }

    if (text.includes('ة') && text.includes('ه')) {
      issues.push('Mixed usage of taa marbouta and haa');
      suggestions.push('Use appropriate taa marbouta (ة) for feminine endings');
    }

    // Check for proper punctuation
    if (!text.match(/[.!?؟،]/)) {
      suggestions.push('Add appropriate Arabic punctuation');
    }

    // Check for RTL markers
    if (this.containsArabic(text) && !this.hasRTLMarkers(text)) {
      suggestions.push('Consider adding RTL directional markers for better display');
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * Generate Arabic prompt for AI with Syrian context
   */
  generateSyrianContextPrompt(
    userRole: 'CLIENT' | 'EXPERT',
    sessionType: string,
    currentStep: string,
    dialect: keyof typeof SYRIAN_DIALECTS = 'general'
  ): string {
    const dialectInfo = SYRIAN_DIALECTS[dialect];
    const roleArabic = userRole === 'EXPERT' ? 'الخبراء' : 'العملاء';

    return `أنت مساعد ذكي متخصص في منصة فريلا سوريا للعمل الحر. تساعد ${roleArabic} السوريين في ${sessionType}.

السياق الثقافي السوري:
- اللهجة: ${dialectInfo.name}
- التعابير المحلية: ${dialectInfo.expressions.join('، ')}
- المرحلة الحالية: ${currentStep}

إرشادات الاستجابة:
1. استخدم اللغة العربية الفصحى مع تعابير محلية سورية مناسبة
2. راعي الثقافة السورية والتقاليد المحلية
3. اعتبر الظروف الاقتصادية في سوريا
4. استخدم أمثلة من السوق السوري عند الإمكان
5. كن مشجعاً ومتفائلاً ومهنياً
6. استخدم علامات الترقيم العربية المناسبة
7. اكتب النصوص بطريقة تدعم الاتجاه من اليمين إلى اليسار

تذكر: أنت تمثل منصة سورية وتخدم المجتمع السوري، فكن فخوراً بهذا الانتماء.`;
  }

  // Private helper methods
  private containsArabic(text: string): boolean {
    return /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(text);
  }

  private hasRTLMarkers(text: string): boolean {
    return Object.values(RTL_MARKERS).some(marker => text.includes(marker));
  }

  private addDirectionalMarkers(text: string): string {
    // Add RLM at the beginning if text starts with Arabic
    if (this.containsArabic(text.charAt(0))) {
      text = RTL_MARKERS.RLM + text;
    }

    // Add RLM at the end if text ends with Arabic
    if (this.containsArabic(text.charAt(text.length - 1))) {
      text = text + RTL_MARKERS.RLM;
    }

    return text;
  }

  private convertToArabicNumerals(text: string): string {
    return text.replace(/[0-9]/g, (digit) => ARABIC_NUMERALS[digit as keyof typeof ARABIC_NUMERALS] || digit);
  }

  private preserveEnglishNumbers(text: string): string {
    // Wrap English numbers with LTR markers
    return text.replace(/(\d+)/g, `${RTL_MARKERS.LRM}$1${RTL_MARKERS.LRM}`);
  }

  private detectFormality(text: string): 'formal' | 'informal' | 'professional' {
    const formalIndicators = ['حضرتك', 'سيادتك', 'المحترم', 'تفضلوا'];
    const informalIndicators = ['أهلين', 'يلا', 'شو', 'كيفك'];
    const professionalIndicators = ['خدمة', 'مشروع', 'عمل', 'خبرة', 'مهارة'];

    const formalCount = formalIndicators.filter(word => text.includes(word)).length;
    const informalCount = informalIndicators.filter(word => text.includes(word)).length;
    const professionalCount = professionalIndicators.filter(word => text.includes(word)).length;

    if (professionalCount > 0) return 'professional';
    if (formalCount > informalCount) return 'formal';
    return 'informal';
  }

  private getRandomElement<T>(array: readonly T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }
}

// Export singleton instance
export const arabicRTLService = new ArabicRTLService();

// Helper functions for easy integration
export const rtlHelpers = {
  /**
   * Quick Arabic text processing
   */
  processText: (text: string, options?: RTLTextOptions) => 
    arabicRTLService.processArabicText(text, options),

  /**
   * Create Syrian cultural response
   */
  createResponse: (message: string, userRole: 'CLIENT' | 'EXPERT', dialect?: keyof typeof SYRIAN_DIALECTS) =>
    arabicRTLService.createCulturalResponse(message, userRole, dialect),

  /**
   * Format mixed content
   */
  formatMixed: (content: string) => arabicRTLService.formatMixedContent(content),

  /**
   * Generate Syrian AI prompt
   */
  generatePrompt: (userRole: 'CLIENT' | 'EXPERT', sessionType: string, currentStep: string, dialect?: keyof typeof SYRIAN_DIALECTS) =>
    arabicRTLService.generateSyrianContextPrompt(userRole, sessionType, currentStep, dialect),
};

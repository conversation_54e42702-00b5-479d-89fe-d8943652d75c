import { Request, Response, NextFunction } from 'express';
import { jwtUtils, authUtils, sessionUtils } from '../utils/auth';
import { unifiedSessionManager } from '../services/unifiedSessionManager';
import { logger, logSecurityEvent } from '../utils/logger';
import { dbService } from '@freela/database';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        sessionId: string;
        firstName: string;
        lastName: string;
        status: string;
        emailVerified: boolean;
        phoneVerified: boolean;
      };
    }
  }
}

/**
 * Authentication middleware - verifies JWT token and loads user data
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required',
        code: 'TOKEN_MISSING',
      });
    }

    // Verify token
    const payload = jwtUtils.verifyAccessToken(token);
    if (!payload) {
      logSecurityEvent('invalid_token_attempt', { token: token.substring(0, 20) + '...' }, req);
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired access token',
        code: 'TOKEN_INVALID',
      });
    }

    // Validate session using unified session manager with fallback to legacy
    let isSessionValid = false;

    // First try unified session manager
    const unifiedSession = await unifiedSessionManager.getSession(payload.sessionId, payload.userId);
    if (unifiedSession && unifiedSession.sessionType === 'auth' && unifiedSession.status === 'active') {
      isSessionValid = true;
      // Update session activity
      await unifiedSessionManager.updateSession(payload.sessionId, {
        metadata: { lastActivity: new Date().toISOString() }
      }, payload.userId);
    } else {
      // Fallback to legacy session validation
      isSessionValid = await sessionUtils.validateSession(payload.sessionId);
    }

    if (!isSessionValid) {
      logSecurityEvent('invalid_session_attempt', { sessionId: payload.sessionId, userId: payload.userId }, req);
      return res.status(401).json({
        success: false,
        message: 'Session has expired',
        code: 'SESSION_EXPIRED',
      });
    }

    // Load user data from database
    let user;
    try {
      user = await dbService.findUserById(payload.userId, {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
      });
    } catch (dbError) {
      logger.error('Database error during user lookup', {
        error: dbError,
        userId: payload.userId,
        sessionId: payload.sessionId
      });

      logSecurityEvent('database_error_during_auth', {
        userId: payload.userId,
        sessionId: payload.sessionId,
        error: dbError instanceof Error ? dbError.message : 'Unknown error'
      }, req);

      return res.status(500).json({
        success: false,
        message: 'Authentication service temporarily unavailable',
        code: 'AUTH_SERVICE_ERROR',
      });
    }

    if (!user) {
      logSecurityEvent('user_not_found', { userId: payload.userId }, req);
      return res.status(401).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND',
      });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      logSecurityEvent('inactive_user_attempt', { userId: user.id, status: user.status }, req);
      return res.status(403).json({
        success: false,
        message: 'Account is not active',
        code: 'ACCOUNT_INACTIVE',
      });
    }

    // Attach user to request
    req.user = {
      ...user,
      sessionId: payload.sessionId,
    };

    next();
  } catch (error) {
    logger.error('Authentication middleware error', { error });
    return res.status(500).json({
      success: false,
      message: 'Authentication failed',
      code: 'AUTH_ERROR',
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuthenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authUtils.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return next();
    }
    
    // Try to authenticate, but don't fail if it doesn't work
    const payload = jwtUtils.verifyAccessToken(token);
    if (payload) {
      // Try unified session manager first, fallback to legacy
      let isSessionValid = false;
      const unifiedSession = await unifiedSessionManager.getSession(payload.sessionId, payload.userId);
      if (unifiedSession && unifiedSession.sessionType === 'auth' && unifiedSession.status === 'active') {
        isSessionValid = true;
        await unifiedSessionManager.updateSession(payload.sessionId, {
          metadata: { lastActivity: new Date().toISOString() }
        }, payload.userId);
      } else {
        isSessionValid = await sessionUtils.validateSession(payload.sessionId);
        if (isSessionValid) {
          await sessionUtils.updateSessionActivity(payload.sessionId);
        }
      }

      if (isSessionValid) {
        try {
          const user = await dbService.findUserById(payload.userId, {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            emailVerified: true,
            phoneVerified: true,
          });

          if (user && user.status === 'ACTIVE') {
            req.user = {
              ...user,
              sessionId: payload.sessionId,
            };
          }
        } catch (dbError) {
          // In optional auth, we silently fail on database errors
          logger.debug('Database error in optional auth, continuing without user', {
            error: dbError,
            userId: payload.userId
          });
        }
      }
    }
    
    next();
  } catch (error) {
    logger.error('Optional authentication middleware error', { error });
    next(); // Continue without authentication
  }
};

/**
 * Role-based authorization middleware
 */
export const authorize = (...allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED',
      });
    }
    
    if (!allowedRoles.includes(req.user.role)) {
      logSecurityEvent('unauthorized_access_attempt', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        endpoint: req.path,
      }, req);
      
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
      });
    }
    
    next();
  };
};

/**
 * Email verification requirement middleware
 */
export const requireEmailVerification = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED',
    });
  }
  
  if (!req.user.emailVerified) {
    return res.status(403).json({
      success: false,
      message: 'Email verification required',
      code: 'EMAIL_VERIFICATION_REQUIRED',
    });
  }
  
  next();
};

/**
 * Phone verification requirement middleware
 */
export const requirePhoneVerification = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED',
    });
  }
  
  if (!req.user.phoneVerified) {
    return res.status(403).json({
      success: false,
      message: 'Phone verification required',
      code: 'PHONE_VERIFICATION_REQUIRED',
    });
  }
  
  next();
};

/**
 * Resource ownership middleware - checks if user owns the resource
 */
export const requireOwnership = (resourceIdParam: string = 'id', userIdField: string = 'userId') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED',
      });
    }
    
    const resourceId = req.params[resourceIdParam];
    if (!resourceId) {
      return res.status(400).json({
        success: false,
        message: 'Resource ID is required',
        code: 'RESOURCE_ID_REQUIRED',
      });
    }
    
    try {
      // This is a generic implementation - you might need to customize based on your models
      // For now, we'll check if the user ID matches
      if (userIdField === 'userId' && resourceId !== req.user.id) {
        logSecurityEvent('unauthorized_resource_access', {
          userId: req.user.id,
          resourceId,
          resourceType: req.route?.path || req.path,
        }, req);
        
        return res.status(403).json({
          success: false,
          message: 'Access denied to this resource',
          code: 'ACCESS_DENIED',
        });
      }
      
      next();
    } catch (error) {
      logger.error('Ownership check error', { error, resourceId, userId: req.user.id });
      return res.status(500).json({
        success: false,
        message: 'Failed to verify resource ownership',
        code: 'OWNERSHIP_CHECK_ERROR',
      });
    }
  };
};

/**
 * Admin only middleware
 */
export const adminOnly = authorize('ADMIN');

/**
 * Expert only middleware
 */
export const expertOnly = authorize('EXPERT');

/**
 * Client only middleware
 */
export const clientOnly = authorize('CLIENT');

/**
 * Expert or Admin middleware
 */
export const expertOrAdmin = authorize('EXPERT', 'ADMIN');

/**
 * Client or Admin middleware
 */
export const clientOrAdmin = authorize('CLIENT', 'ADMIN');

/**
 * Any authenticated user middleware
 */
export const anyUser = authorize('CLIENT', 'EXPERT', 'ADMIN');
